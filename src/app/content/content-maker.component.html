<div class="content-maker-container">
  <!-- Loading state -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading content...</p>
  </div>

  <!-- Error state -->
  <div *ngIf="errorMessage && !isLoading" class="error-container">
    <div class="error-message">
      <h3>Error Loading Content</h3>
      <p>{{ errorMessage }}</p>
      <button (click)="reloadContent()" class="retry-button">
        Try Again
      </button>
    </div>
  </div>

  <!-- Success state -->
  <iframe
    *ngIf="source && !isLoading && !errorMessage"
    [src]="source"
    width="100%"
    height="100%"
    class="content-iframe">
  </iframe>
</div>
