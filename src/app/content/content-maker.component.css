.content-maker-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
}

.error-message {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
  border: 1px solid #e74c3c;
  border-radius: 8px;
  background-color: #fdf2f2;
}

.error-message h3 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.error-message p {
  color: #666;
  margin-bottom: 1.5rem;
}

.retry-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #2980b9;
}

.content-iframe {
  border: none;
  flex: 1;
}
